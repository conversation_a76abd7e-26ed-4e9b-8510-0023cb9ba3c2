import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import { PersonalizationState, Goal } from './types';
import { STORAGE_KEYS, storeData, getData } from './storage';
import { useAuth } from './AuthContext';

// Default personalization state
const defaultPersonalizationState: PersonalizationState = {
  goal: null,
  allergiesRestrictions: [],
  isPersonalizationComplete: false,
};

// Create the context
export const PersonalizationContext = createContext<{
  personalizationState: PersonalizationState;
  setGoal: (goal: Goal) => Promise<void>;
  setAllergiesRestrictions: (allergies: string[]) => Promise<void>;
  completePersonalization: () => Promise<void>;
  resetPersonalization: () => Promise<void>;
}>({
  personalizationState: defaultPersonalizationState,
  setGoal: async () => {},
  setAllergiesRestrictions: async () => {},
  completePersonalization: async () => {},
  resetPersonalization: async () => {},
});

// Custom hook to use the personalization context
export const usePersonalization = () => useContext(PersonalizationContext);

// Provider component
export const PersonalizationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [personalizationState, setPersonalizationState] = useState<PersonalizationState>(defaultPersonalizationState);
  const { authState } = useAuth();

  // Load personalization state from storage on mount and when auth state changes
  useEffect(() => {
    const loadPersonalizationState = async () => {
      try {
        const storedState = await getData<PersonalizationState>(STORAGE_KEYS.PERSONALIZATION);
        if (storedState) {
          setPersonalizationState(storedState);
        } else {
          setPersonalizationState(defaultPersonalizationState);
        }
      } catch (error) {
        console.error('Error loading personalization state:', error);
        setPersonalizationState(defaultPersonalizationState);
      }
    };

    loadPersonalizationState();
  }, [authState.isAuthenticated]); // Reload when auth state changes

  // Set goal function - memoized
  const setGoal = useCallback(async (goal: Goal): Promise<void> => {
    try {
      const newState = {
        ...personalizationState,
        goal,
      };

      // Save to storage
      await storeData(STORAGE_KEYS.PERSONALIZATION, newState);

      // Update state
      setPersonalizationState(newState);
    } catch (error) {
      console.error('Error setting goal:', error);
    }
  }, [personalizationState]);

  // Set allergies/restrictions function - memoized
  const setAllergiesRestrictions = useCallback(async (allergies: string[]): Promise<void> => {
    try {
      const newState = {
        ...personalizationState,
        allergiesRestrictions: allergies,
      };

      // Save to storage
      await storeData(STORAGE_KEYS.PERSONALIZATION, newState);

      // Update state
      setPersonalizationState(newState);
    } catch (error) {
      console.error('Error setting allergies/restrictions:', error);
    }
  }, [personalizationState]);

  // Complete personalization function - memoized
  const completePersonalization = useCallback(async (): Promise<void> => {
    try {
      const newState = {
        ...personalizationState,
        isPersonalizationComplete: true,
      };

      // Save to storage
      await storeData(STORAGE_KEYS.PERSONALIZATION, newState);

      // Update state
      setPersonalizationState(newState);
    } catch (error) {
      console.error('Error completing personalization:', error);
    }
  }, [personalizationState]);

  // Reset personalization function - memoized
  const resetPersonalization = useCallback(async (): Promise<void> => {
    try {
      // Save default state to storage
      await storeData(STORAGE_KEYS.PERSONALIZATION, defaultPersonalizationState);

      // Update state
      setPersonalizationState(defaultPersonalizationState);
    } catch (error) {
      console.error('Error resetting personalization:', error);
    }
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    personalizationState,
    setGoal,
    setAllergiesRestrictions,
    completePersonalization,
    resetPersonalization,
  }), [personalizationState, setGoal, setAllergiesRestrictions, completePersonalization, resetPersonalization]);

  return (
    <PersonalizationContext.Provider value={contextValue}>
      {children}
    </PersonalizationContext.Provider>
  );
};
