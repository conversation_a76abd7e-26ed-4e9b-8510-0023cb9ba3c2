import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import { AuthState, User, HistoryState, ScanState } from './types';
import {
  STORAGE_KEYS,
  storeData,
  getData,
  removeData,
  migrateAnonymousToAuthenticated,
  migrateLegacyData,
  clearAllData
} from './storage';
import { isDuplicateScan } from './utils/duplicateDetection';

// Default auth state
const defaultAuthState: AuthState = {
  isAuthenticated: false,
  user: null,
  isLoading: true,
  error: null,
};

// Create the context
export const AuthContext = createContext<{
  authState: AuthState;
  login: (phoneNumber: string) => Promise<void>;
  logout: () => Promise<void>;
  verifyOTP: (phoneNumber: string, otp: string) => Promise<boolean>;
  clearError: () => void;
  migrateAnonymousData: () => Promise<void>;
  clearUserData: () => Promise<void>;
  isAuthenticated: () => boolean;
}>({
  authState: defaultAuthState,
  login: async () => {},
  logout: async () => {},
  verifyOTP: async () => false,
  clearError: () => {},
  migrateAnonymousData: async () => {},
  clearUserData: async () => {},
  isAuthenticated: () => false,
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>(defaultAuthState);

  // Helper function to check if user is authenticated - memoized
  const isAuthenticated = useCallback((): boolean => {
    return authState.isAuthenticated && !!authState.user;
  }, [authState.isAuthenticated, authState.user]);

  // Migrate anonymous data to authenticated storage - memoized
  const migrateAnonymousData = useCallback(async (): Promise<void> => {
    try {
      // Only migrate if authenticated
      if (!isAuthenticated()) {
        return;
      }

      console.log('Migrating anonymous data to authenticated storage...');

      // Migrate scan history with improved duplicate detection
      await migrateAnonymousToAuthenticated<HistoryState>(
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        (authenticatedData, anonymousData) => {
          // If both exist, merge them with better duplicate detection
          if (authenticatedData && anonymousData) {
            const mergedScans = [...authenticatedData.scans];

            // Add anonymous scans that don't already exist in authenticated history
            anonymousData.scans.forEach(anonymousScan => {
              // Use the utility function for duplicate detection
              if (!isDuplicateScan(anonymousScan, mergedScans)) {
                mergedScans.push(anonymousScan);
              }
            });

            // Sort by timestamp (newest first)
            mergedScans.sort((a, b) =>
              new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
            );

            return {
              scans: mergedScans,
              isLoading: false,
              error: null
            };
          }

          // If only anonymous data exists, use it
          return anonymousData;
        }
      );

      // Migrate current scan
      await migrateAnonymousToAuthenticated<ScanState>(
        STORAGE_KEYS.ANONYMOUS.CURRENT_SCAN,
        STORAGE_KEYS.AUTHENTICATED.CURRENT_SCAN
      );

      // Migrate subscription data (from legacy to authenticated)
      await migrateAnonymousToAuthenticated(
        STORAGE_KEYS.LEGACY.SUBSCRIPTION,
        STORAGE_KEYS.AUTHENTICATED.SUBSCRIPTION
      );

      console.log('Data migration complete');
    } catch (error) {
      console.error('Error migrating anonymous data:', error);
    }
  }, [isAuthenticated]);

  // Load auth state from storage on mount and migrate legacy data
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // First, migrate any legacy data to the new storage structure
        await migrateLegacyData();

        // Then load auth state
        const storedAuthState = await getData<AuthState>(STORAGE_KEYS.AUTH);
        if (storedAuthState) {
          setAuthState(storedAuthState);

          // If authenticated, migrate any anonymous data
          if (storedAuthState.isAuthenticated) {
            await migrateAnonymousData();
          }
        } else {
          setAuthState({ ...defaultAuthState, isLoading: false });
        }
      } catch (error) {
        console.error('Error initializing app:', error);
        setAuthState({ ...defaultAuthState, isLoading: false });
      }
    };

    initializeApp();
  }, []);

  // Login function - in a real app, this would make an API call - memoized
  const login = useCallback(async (phoneNumber: string): Promise<void> => {
    try {
      setAuthState({ ...authState, isLoading: true, error: null });

      // In a real app, you would make an API call here to send OTP
      // For now, we'll just simulate a successful OTP send

      // Update state to indicate OTP has been sent
      setAuthState({
        ...authState,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      setAuthState({
        ...authState,
        isLoading: false,
        error: 'Failed to send OTP. Please try again.',
      });
    }
  }, [authState]);

  // Verify OTP function - in a real app, this would make an API call - memoized
  const verifyOTP = useCallback(async (phoneNumber: string, otp: string): Promise<boolean> => {
    try {
      setAuthState({ ...authState, isLoading: true, error: null });

      // In a real app, you would make an API call here to verify OTP
      // For now, we'll accept any OTP to allow testing the flow
      console.log('Verifying OTP:', otp);

      // Create a mock user
      const user: User = {
        id: 'user-' + Date.now(),
        phoneNumber,
        createdAt: new Date().toISOString(),
      };

      const newAuthState: AuthState = {
        isAuthenticated: true,
        user,
        isLoading: false,
        error: null,
      };

      // Save to storage
      await storeData(STORAGE_KEYS.AUTH, newAuthState);

      // Update state
      setAuthState(newAuthState);

      // Migrate anonymous data to authenticated storage
      await migrateAnonymousData();

      return true;

    } catch (error) {
      console.error('OTP verification error:', error);
      setAuthState({
        ...authState,
        isLoading: false,
        error: 'Failed to verify OTP. Please try again.',
      });
      return false;
    }
  }, [authState, migrateAnonymousData]);

  // Clear all user data (used for complete account reset) - memoized
  const clearUserData = useCallback(async (): Promise<void> => {
    try {
      // Completely clear AsyncStorage using shared utility
      await clearAllData();

      console.log('All AsyncStorage data cleared');
    } catch (error) {
      console.error('Error clearing user data:', error);
      throw error;
    }
  }, []);

  // Logout function - preserves personalization preferences - memoized
  const logout = useCallback(async (): Promise<void> => {
    try {
      setAuthState({ ...authState, isLoading: true });

      // Remove auth data from storage
      await removeData(STORAGE_KEYS.AUTH);

      // Remove authenticated user-specific data from storage
      // Note: We're not removing personalization preferences
      await removeData(STORAGE_KEYS.AUTHENTICATED.CURRENT_SCAN);
      await removeData(STORAGE_KEYS.AUTHENTICATED.HISTORY);
      await removeData(STORAGE_KEYS.AUTHENTICATED.SUBSCRIPTION);

      // Reset state
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null,
      });

      console.log('Logout successful');
    } catch (error) {
      console.error('Error during logout:', error);
      setAuthState({
        ...authState,
        isLoading: false,
        error: 'Failed to logout. Please try again.',
      });
    }
  }, [authState]);

  // Clear error function - memoized
  const clearError = useCallback(() => {
    setAuthState({ ...authState, error: null });
  }, [authState]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    authState,
    login,
    logout,
    verifyOTP,
    clearError,
    migrateAnonymousData,
    clearUserData,
    isAuthenticated,
  }), [authState, login, logout, verifyOTP, clearError, migrateAnonymousData, clearUserData, isAuthenticated]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
